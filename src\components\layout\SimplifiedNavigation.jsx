import React, { useState, useEffect } from 'react';
import './SimplifiedNavigation.css';

const SimplifiedNavigation = ({
  currentSection = 'home',
  onNavigate,
  userRole,
  ADMIN_ROLES,
  isAuthenticated = false
}) => {
  const [activeSection, setActiveSection] = useState(currentSection);
  const [showSecondaryNav, setShowSecondaryNav] = useState(false);
  const [secondarySection, setSecondarySection] = useState(null);
  const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 768);
  const [hoverTimeout, setHoverTimeout] = useState(null);

  // Handle window resize to detect desktop vs mobile
  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
    };
  }, [hoverTimeout]);

  const handlePrimaryNavigation = (section) => {
    setActiveSection(section);
    setSecondarySection(section);
    setShowSecondaryNav(true);
    if (onNavigate) {
      onNavigate(section);
    }
  };

  const handleSecondaryNavigation = (section, subsection) => {
    setShowSecondaryNav(false);
    if (onNavigate) {
      onNavigate(section, subsection);
    }
  };

  // Desktop hover handlers
  const handleMouseEnter = (section) => {
    if (!isDesktop) return;

    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
    }

    // Show secondary nav after a short delay
    const timeout = setTimeout(() => {
      setSecondarySection(section);
      setShowSecondaryNav(true);
    }, 200);

    setHoverTimeout(timeout);
  };

  const handleMouseLeave = () => {
    if (!isDesktop) return;

    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
    }

    // Hide secondary nav after a short delay
    const timeout = setTimeout(() => {
      setShowSecondaryNav(false);
      setSecondarySection(null);
    }, 300);

    setHoverTimeout(timeout);
  };

  const handleSecondaryMouseEnter = () => {
    if (!isDesktop) return;

    // Clear hide timeout when hovering over secondary nav
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
    }
  };

  const handleSecondaryMouseLeave = () => {
    if (!isDesktop) return;

    // Hide secondary nav when leaving secondary area
    const timeout = setTimeout(() => {
      setShowSecondaryNav(false);
      setSecondarySection(null);
    }, 300);

    setHoverTimeout(timeout);
  };

  const primaryNavItems = [
    {
      id: 'home',
      label: 'Home',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
          <polyline points="9,22 9,12 15,12 15,22"></polyline>
        </svg>
      ),
      description: 'Community Feed'
    },
    {
      id: 'stories',
      label: 'Stories',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
          <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
        </svg>
      ),
      description: 'Share & Discover'
    },
    {
      id: 'connect',
      label: 'Connect',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      ),
      description: 'Community & Economy'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: (
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      ),
      description: 'Account & Settings'
    }
  ];

  const secondaryNavItems = {
    home: [
      { id: 'feed', label: 'Community Feed', description: 'Latest stories and updates' },
      { id: 'featured', label: 'Featured Content', description: 'Highlighted community stories' },
      { id: 'announcements', label: 'Announcements', description: 'Platform news and updates' }
    ],
    stories: [
      { id: 'share', label: 'Share Your Story', description: 'Tell your narrative' },
      { id: 'browse', label: 'Browse Stories', description: 'Discover community stories' },
      { id: 'featured-story', label: 'Story of the Month', description: 'Featured community narrative' }
    ],
    connect: [
      { id: 'economic-hub', label: 'Economic Hub', description: 'Business opportunities' },
      { id: 'community-dialogue', label: 'Community Dialogue', description: 'Discussion forums' },
      { id: 'support-requests', label: 'Support Requests', description: 'Community assistance' }
    ],
    profile: [
      { id: 'account', label: 'Account Settings', description: 'Personal information' },
      { id: 'privacy', label: 'Privacy Settings', description: 'Control your data' },
      ...(userRole && userRole.role !== ADMIN_ROLES?.USER ? [
        { id: 'admin', label: 'Admin Dashboard', description: 'Platform management' }
      ] : [])
    ]
  };

  if (!isAuthenticated) {
    return null; // Don't show navigation for unauthenticated users
  }

  return (
    <nav className="simplified-navigation">
      {/* Primary Navigation */}
      <div className="simplified-navigation__primary">
        <div className="simplified-navigation__container">
          {primaryNavItems.map((item) => (
            <button
              key={item.id}
              className={`simplified-navigation__primary-item ${
                activeSection === item.id ? 'simplified-navigation__primary-item--active' : ''
              }`}
              onClick={() => {
                if (isDesktop) {
                  // On desktop, clicking should navigate directly to the section
                  setActiveSection(item.id);
                  if (onNavigate) {
                    onNavigate(item.id);
                  }
                } else {
                  // On mobile, use the existing behavior
                  handlePrimaryNavigation(item.id);
                }
              }}
              onMouseEnter={() => handleMouseEnter(item.id)}
              onMouseLeave={handleMouseLeave}
              aria-label={`Navigate to ${item.label}: ${item.description}`}
            >
              <div className="simplified-navigation__icon">
                {item.icon}
              </div>
              <div className="simplified-navigation__label">
                {item.label}
              </div>
              <div className="simplified-navigation__description">
                {item.description}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Secondary Navigation Overlay */}
      {showSecondaryNav && secondarySection && (
        <>
          <div 
            className="simplified-navigation__overlay"
            onClick={() => setShowSecondaryNav(false)}
          />
          <div
            className="simplified-navigation__secondary"
            onMouseEnter={handleSecondaryMouseEnter}
            onMouseLeave={handleSecondaryMouseLeave}
          >
            <div className="simplified-navigation__secondary-header">
              <h3 className="simplified-navigation__secondary-title">
                {primaryNavItems.find(item => item.id === secondarySection)?.label}
              </h3>
              <button 
                className="simplified-navigation__close-btn"
                onClick={() => setShowSecondaryNav(false)}
                aria-label="Close navigation menu"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
            
            <div className="simplified-navigation__secondary-content">
              {secondaryNavItems[secondarySection]?.map((item) => (
                <button
                  key={item.id}
                  className="simplified-navigation__secondary-item"
                  onClick={() => handleSecondaryNavigation(secondarySection, item.id)}
                >
                  <div className="simplified-navigation__secondary-label">
                    {item.label}
                  </div>
                  <div className="simplified-navigation__secondary-description">
                    {item.description}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </nav>
  );
};

export default SimplifiedNavigation;
