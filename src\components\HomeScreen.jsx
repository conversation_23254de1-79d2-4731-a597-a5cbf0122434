import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import { db } from '../firebase';
import { collection, query, orderBy, limit, onSnapshot } from 'firebase/firestore';
import StoryForm from './StoryForm';
import SearchAndFilters from './SearchAndFilters';
import CommunityStats from './CommunityStats';

/**
 * Home Screen Component
 * Main landing page that displays feed content, stories, and community highlights
 */
export default function HomeScreen({
  stories,
  onStorySubmit,
  onStoryUpdate,
  onStoryDelete,
  showCelebration,
  setShowCelebration,
  onFormClear
}) {
  const { currentUser } = useAuth();
  const [feedItems, setFeedItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'stories', 'community', 'trending'
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('newest');

  useEffect(() => {
    if (!currentUser) {
      setLoading(false);
      return;
    }

    // Load recent community activity for the feed
    const unsubscribers = [];

    try {
      // Stories
      const storiesQuery = query(
        collection(db, 'stories'),
        orderBy('createdAt', 'desc'),
        limit(10)
      );
      unsubscribers.push(
        onSnapshot(storiesQuery, (snapshot) => {
          const recentStories = snapshot.docs.map(doc => ({
            id: doc.id,
            type: 'story',
            ...doc.data(),
            timestamp: doc.data().createdAt?.toDate() || new Date()
          }));
          updateFeedItems('stories', recentStories);
        }, (error) => {
          console.error('Error loading stories for feed:', error);
        })
      );

      // Support Requests
      const supportQuery = query(
        collection(db, 'supportRequests'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      unsubscribers.push(
        onSnapshot(supportQuery, (snapshot) => {
          const supportItems = snapshot.docs.map(doc => ({
            id: doc.id,
            type: 'support',
            ...doc.data(),
            timestamp: doc.data().createdAt?.toDate() || new Date()
          }));
          updateFeedItems('support', supportItems);
        }, (error) => {
          console.error('Error loading support requests for feed:', error);
        })
      );

      // Community Discussions
      const discussionsQuery = query(
        collection(db, 'discussions'),
        orderBy('createdAt', 'desc'),
        limit(5)
      );
      unsubscribers.push(
        onSnapshot(discussionsQuery, (snapshot) => {
          const discussions = snapshot.docs.map(doc => ({
            id: doc.id,
            type: 'discussion',
            ...doc.data(),
            timestamp: doc.data().createdAt?.toDate() || new Date()
          }));
          updateFeedItems('discussions', discussions);
        }, (error) => {
          console.error('Error loading discussions for feed:', error);
        })
      );

      setLoading(false);
    } catch (error) {
      console.error('Error setting up feed listeners:', error);
      setLoading(false);
    }

    // Cleanup timeout
    const loadingTimeout = setTimeout(() => {
      setLoading(false);
    }, 3000);

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
      clearTimeout(loadingTimeout);
    };
  }, [currentUser]);

  const updateFeedItems = (type, items) => {
    setFeedItems(prevItems => {
      const filteredItems = prevItems.filter(item => item.type !== type);
      const newItems = [...filteredItems, ...items];
      return newItems.sort((a, b) => b.timestamp - a.timestamp);
    });
  };

  const filteredFeedItems = feedItems.filter(item => {
    if (activeTab !== 'all' && item.type !== activeTab) return false;
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        item.title?.toLowerCase().includes(searchLower) ||
        item.content?.toLowerCase().includes(searchLower) ||
        item.description?.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  const sortedFeedItems = [...filteredFeedItems].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return a.timestamp - b.timestamp;
      case 'popular':
        return (b.likes?.length || 0) - (a.likes?.length || 0);
      default: // newest
        return b.timestamp - a.timestamp;
    }
  });

  const renderFeedItem = (item) => {
    const timeAgo = getTimeAgo(item.timestamp);
    
    switch (item.type) {
      case 'story':
        return (
          <div key={item.id} className="feed-item story-item">
            <div className="feed-item-header">
              <div className="item-type">📖 Story</div>
              <div className="item-time">{timeAgo}</div>
            </div>
            <h3 className="item-title">{item.title}</h3>
            <p className="item-content">{item.content?.substring(0, 200)}...</p>
            <div className="item-tags">
              {item.tags?.map(tag => (
                <span key={tag} className="tag">#{tag}</span>
              ))}
            </div>
            <div className="item-actions">
              <button className="action-btn">
                ❤️ {item.likes?.length || 0}
              </button>
              <button className="action-btn">
                💬 {item.comments?.length || 0}
              </button>
              <button className="action-btn">
                🔗 Share
              </button>
              {/* Delete button for story author */}
              {currentUser && item.author === currentUser.uid && (
                <button
                  className="action-btn delete-btn"
                  onClick={() => onStoryDelete(item.id, item.title, item.content)}
                  title="Delete your story"
                  style={{
                    backgroundColor: '#ff4757',
                    color: 'white',
                    marginLeft: '8px'
                  }}
                >
                  🗑️
                </button>
              )}
            </div>
          </div>
        );
      
      case 'support':
        return (
          <div key={item.id} className="feed-item support-item">
            <div className="feed-item-header">
              <div className="item-type">🤝 Support Request</div>
              <div className="item-time">{timeAgo}</div>
            </div>
            <h3 className="item-title">{item.title}</h3>
            <p className="item-content">{item.description?.substring(0, 150)}...</p>
            <div className="support-details">
              <span className="support-type">{item.supportType}</span>
              <span className="urgency">{item.urgency}</span>
            </div>
          </div>
        );
      
      case 'discussion':
        return (
          <div key={item.id} className="feed-item discussion-item">
            <div className="feed-item-header">
              <div className="item-type">🗣️ Discussion</div>
              <div className="item-time">{timeAgo}</div>
            </div>
            <h3 className="item-title">{item.topic}</h3>
            <p className="item-content">{item.description?.substring(0, 150)}...</p>
            <div className="discussion-stats">
              <span>👥 {item.participants?.length || 0} participants</span>
              <span>💬 {item.messages?.length || 0} messages</span>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  const getTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <div className="home-screen">
      {/* Welcome Header */}
      <div className="home-header">
        <h1>Welcome to NAROOP</h1>
        <p>Your community hub for stories, support, and empowerment</p>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <button 
          className="quick-action-btn primary"
          onClick={() => document.getElementById('story-form')?.scrollIntoView({ behavior: 'smooth' })}
        >
          📝 Share Your Story
        </button>
        <button className="quick-action-btn">
          🤝 Request Support
        </button>
        <button className="quick-action-btn">
          💰 Economic Hub
        </button>
        <button className="quick-action-btn">
          🗣️ Join Discussion
        </button>
      </div>

      {/* Community Stats */}
      <CommunityStats stories={stories || []} />

      {/* Feed Tabs */}
      <div className="feed-tabs">
        <button 
          className={`feed-tab ${activeTab === 'all' ? 'active' : ''}`}
          onClick={() => setActiveTab('all')}
        >
          All Activity
        </button>
        <button 
          className={`feed-tab ${activeTab === 'stories' ? 'active' : ''}`}
          onClick={() => setActiveTab('stories')}
        >
          Stories
        </button>
        <button 
          className={`feed-tab ${activeTab === 'support' ? 'active' : ''}`}
          onClick={() => setActiveTab('support')}
        >
          Support
        </button>
        <button 
          className={`feed-tab ${activeTab === 'discussions' ? 'active' : ''}`}
          onClick={() => setActiveTab('discussions')}
        >
          Discussions
        </button>
      </div>

      {/* Search and Filters */}
      <SearchAndFilters
        searchQuery={searchTerm}
        onSearchChange={setSearchTerm}
        selectedTopic="All Topics"
        onTopicChange={() => {}}
        sortBy={sortBy}
        onSortChange={setSortBy}
        showBookmarksOnly={false}
        onBookmarksToggle={() => {}}
        bookmarkCount={0}
        TOPICS={['All Topics', 'Personal Growth', 'Community', 'Success', 'Challenges', 'Family', 'Career', 'Education', 'Health', 'Spirituality']}
        stories={stories || []}
      />

      {/* Feed Content */}
      <div className="home-feed">
        {loading ? (
          <div className="feed-loading">
            <div className="loading-spinner"></div>
            <p>Loading community activity...</p>
          </div>
        ) : sortedFeedItems.length > 0 ? (
          <div className="feed-items">
            {sortedFeedItems.map(renderFeedItem)}
          </div>
        ) : (
          <div className="empty-feed">
            <div className="empty-icon">🌟</div>
            <h3>Welcome to your community!</h3>
            <p>Be the first to share a story or start a discussion.</p>
            <button 
              className="cta-btn"
              onClick={() => document.getElementById('story-form')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Share Your First Story
            </button>
          </div>
        )}
      </div>

      {/* Story Form */}
      <div id="story-form">
        <StoryForm
          onSubmit={onStorySubmit}
          onFormClear={onFormClear}
          showCelebration={showCelebration}
          setShowCelebration={setShowCelebration}
        />
      </div>
    </div>
  );
}
