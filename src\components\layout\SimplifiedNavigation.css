/* NAROOP Simplified Navigation - Black & Gold Theme */

.simplified-navigation {
  position: relative;
  z-index: 900;
}

/* Primary Navigation */
.simplified-navigation__primary {
  background: var(--color-heritage-cream);
  border-bottom: 2px solid var(--color-heritage-gold);
  box-shadow: 0 2px 8px rgba(26, 26, 26, 0.1);
}

.simplified-navigation__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-xs);
}

.simplified-navigation__primary-item {
  background: transparent;
  border: none;
  padding: var(--space-md) var(--space-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  border-radius: 8px;
  margin: var(--space-xs) 0;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  position: relative;
  overflow: hidden;
}

.simplified-navigation__primary-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: var(--color-heritage-gold);
  transition: width 0.3s ease;
}

.simplified-navigation__primary-item:hover::before,
.simplified-navigation__primary-item--active::before {
  width: 80%;
}

.simplified-navigation__primary-item:hover {
  background: var(--color-prosperity-champagne);
  transform: translateY(-2px);
}

.simplified-navigation__primary-item--active {
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-empowerment-amber) 100%);
  color: var(--color-heritage-black);
}

.simplified-navigation__icon {
  width: 24px;
  height: 24px;
  stroke-width: 2;
  color: inherit;
}

.simplified-navigation__label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin: 0;
}

.simplified-navigation__primary-item--active .simplified-navigation__label {
  color: var(--color-heritage-black);
}

.simplified-navigation__description {
  font-size: 0.75rem;
  color: var(--color-heritage-forest);
  margin: 0;
  opacity: 0.8;
}

.simplified-navigation__primary-item--active .simplified-navigation__description {
  color: var(--color-heritage-black);
  opacity: 0.7;
}

/* Secondary Navigation Overlay */
.simplified-navigation__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 26, 26, 0.5);
  z-index: 998;
  backdrop-filter: blur(4px);
}

.simplified-navigation__secondary {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-gold);
  border-radius: 16px;
  box-shadow: 0 16px 48px rgba(26, 26, 26, 0.3);
  z-index: 999;
  min-width: 320px;
  max-width: 480px;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInScale 0.3s ease-out;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.simplified-navigation__secondary-header {
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-empowerment-amber) 100%);
  color: var(--color-heritage-black);
  padding: var(--space-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.simplified-navigation__secondary-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
}

.simplified-navigation__close-btn {
  background: transparent;
  border: 2px solid var(--color-heritage-black);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-heritage-black);
}

.simplified-navigation__close-btn:hover {
  background: var(--color-heritage-black);
  color: var(--color-heritage-gold);
  transform: rotate(90deg);
}

.simplified-navigation__close-btn svg {
  width: 16px;
  height: 16px;
  stroke-width: 2;
}

.simplified-navigation__secondary-content {
  padding: var(--space-sm) 0;
  max-height: 60vh;
  overflow-y: auto;
}

.simplified-navigation__secondary-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: var(--space-md);
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--color-prosperity-champagne);
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--space-xs);
}

.simplified-navigation__secondary-item:last-child {
  border-bottom: none;
}

.simplified-navigation__secondary-item:hover {
  background: var(--color-prosperity-champagne);
  padding-left: var(--space-lg);
}

.simplified-navigation__secondary-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-heritage-black);
  margin: 0;
}

.simplified-navigation__secondary-description {
  font-size: 0.875rem;
  color: var(--color-heritage-forest);
  margin: 0;
  opacity: 0.8;
}

/* Desktop Hover Styles */
@media (min-width: 769px) {
  .simplified-navigation__secondary {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    transform: none;
    background: var(--color-heritage-cream);
    border: 2px solid var(--color-heritage-gold);
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(26, 26, 26, 0.2);
    z-index: 999;
    min-width: auto;
    max-width: none;
    max-height: 400px;
    margin-top: var(--space-xs);
    animation: slideDown 0.2s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .simplified-navigation__secondary-header {
    padding: var(--space-sm) var(--space-md);
  }

  .simplified-navigation__secondary-title {
    font-size: 1rem;
  }

  .simplified-navigation__close-btn {
    display: none; /* Hide close button on desktop */
  }

  .simplified-navigation__secondary-content {
    padding: var(--space-sm);
    max-height: 300px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-xs);
  }

  .simplified-navigation__secondary-item {
    padding: var(--space-sm);
    text-align: left;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .simplified-navigation__secondary-item:hover {
    background: var(--color-prosperity-champagne);
    transform: translateY(-1px);
  }

  .simplified-navigation__overlay {
    display: none; /* Hide overlay on desktop */
  }

  /* Position secondary nav relative to primary nav */
  .simplified-navigation {
    position: relative;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .simplified-navigation__container {
    padding: 0 var(--space-sm);
    gap: 0;
  }

  .simplified-navigation__primary-item {
    padding: var(--space-sm);
    min-height: 70px;
    margin: var(--space-xs) 0;
  }

  .simplified-navigation__label {
    font-size: 0.8rem;
  }

  .simplified-navigation__description {
    font-size: 0.7rem;
  }

  .simplified-navigation__icon {
    width: 20px;
    height: 20px;
  }

  .simplified-navigation__secondary {
    left: var(--space-sm);
    right: var(--space-sm);
    transform: translateY(-50%);
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .simplified-navigation__container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-xs);
  }

  .simplified-navigation__primary-item {
    min-height: 60px;
  }

  .simplified-navigation__description {
    display: none;
  }

  .simplified-navigation__secondary {
    top: 20%;
    bottom: 20%;
    left: var(--space-sm);
    right: var(--space-sm);
    transform: none;
    max-height: none;
    height: auto;
  }

  .simplified-navigation__secondary-content {
    max-height: none;
    height: calc(100% - 80px);
  }
}

/* Focus States for Accessibility */
.simplified-navigation__primary-item:focus,
.simplified-navigation__secondary-item:focus,
.simplified-navigation__close-btn:focus {
  outline: 3px solid var(--color-heritage-gold);
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .simplified-navigation__primary-item,
  .simplified-navigation__secondary-item,
  .simplified-navigation__close-btn {
    transition: none;
  }

  .simplified-navigation__secondary {
    animation: none;
  }

  .simplified-navigation__close-btn:hover {
    transform: none;
  }
}
